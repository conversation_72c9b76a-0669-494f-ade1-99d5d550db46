"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createError = exports.errorHandler = void 0;
const errorHandler = (err, req, res, next) => {
    let statusCode = 500;
    let message = "Internal Server Error";
    let details = null;
    if (err.name === "PrismaClientKnownRequestError") {
        const prismaError = err;
        switch (prismaError.code) {
            case "P2002":
                statusCode = 409;
                message = "A record with this information already exists";
                details = { field: prismaError.meta?.target };
                break;
            case "P2025":
                statusCode = 404;
                message = "Record not found";
                break;
            case "P2003":
                statusCode = 400;
                message = "Foreign key constraint failed";
                break;
            default:
                statusCode = 400;
                message = "Database operation failed";
        }
    }
    else if (err.name === "PrismaClientValidationError") {
        statusCode = 400;
        message = "Invalid data provided";
    }
    else if ("statusCode" in err && err.statusCode) {
        statusCode = err.statusCode;
        message = err.message;
    }
    else if (err.name === "ValidationError") {
        statusCode = 400;
        message = "Validation failed";
    }
    else if (err.name === "JsonWebTokenError") {
        statusCode = 401;
        message = "Invalid token";
    }
    else if (err.name === "TokenExpiredError") {
        statusCode = 401;
        message = "Token expired";
    }
    else if (err.name === "MulterError") {
        statusCode = 400;
        message = "File upload error";
        if (err.message.includes("File too large")) {
            message = "File size too large";
        }
    }
    if (process.env.NODE_ENV === "development") {
        console.error("Error:", err);
    }
    const errorResponse = {
        error: message,
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method,
    };
    if (details) {
        errorResponse.details = details;
    }
    if (process.env.NODE_ENV === "development") {
        errorResponse.stack = err.stack;
    }
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const createError = (message, statusCode = 500) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    error.isOperational = true;
    return error;
};
exports.createError = createError;
//# sourceMappingURL=errorHandler.js.map