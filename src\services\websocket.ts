import { WebSocketServer, WebSocket } from 'ws';

interface ExtendedWebSocket extends WebSocket {
  userId?: string;
  userRole?: string;
}

export const setupWebSocket = (wss: WebSocketServer) => {
  console.log('Setting up WebSocket server...');

  wss.on('connection', (ws: ExtendedWebSocket, req) => {
    console.log('New WebSocket connection established');

    // Handle authentication and user identification
    ws.on('message', (message: string) => {
      try {
        const data = JSON.parse(message);
        
        switch (data.type) {
          case 'auth':
            // TODO: Implement JWT token verification
            ws.userId = data.userId;
            ws.userRole = data.userRole;
            ws.send(JSON.stringify({
              type: 'auth_success',
              message: 'Authentication successful'
            }));
            break;
            
          case 'ping':
            ws.send(JSON.stringify({
              type: 'pong',
              timestamp: Date.now()
            }));
            break;
            
          default:
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Unknown message type'
            }));
        }
      } catch (error) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Invalid message format'
        }));
      }
    });

    ws.on('close', () => {
      console.log('WebSocket connection closed');
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    // Send welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'Connected to Alumni Portal WebSocket server'
    }));
  });

  return wss;
};
