import { Request, Response, NextFunction } from "express";
import { UserStatus } from "@prisma/client";
import { prisma } from "@/config/database";
import { createError } from "@/middleware/errorHandler";
import { sendRealTimeMessage } from "@/services/websocket";

interface SendMessageRequest {
  receiverId: string;
  content: string;
}

/**
 * Get conversations for current user
 */
export const getConversations = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Get all unique conversations (users who have exchanged messages with current user)
    const conversations = await prisma.$queryRaw<
      Array<{
        userId: string;
        name: string;
        profilePicture: string | null;
        role: string;
        company: string | null;
        jobTitle: string | null;
        lastMessageContent: string;
        lastMessageTime: Date;
        unreadCount: number;
      }>
    >`
      SELECT DISTINCT
        CASE 
          WHEN m.senderId = ${req.user.userId} THEN m.receiverId
          ELSE m.senderId
        END as "userId",
        u.name,
        u."profilePicture",
        u.role,
        u.company,
        u."jobTitle",
        (
          SELECT content 
          FROM messages m2 
          WHERE (m2."senderId" = ${req.user.userId} AND m2."receiverId" = u.id) 
             OR (m2."senderId" = u.id AND m2."receiverId" = ${req.user.userId})
          ORDER BY m2."createdAt" DESC 
          LIMIT 1
        ) as "lastMessageContent",
        (
          SELECT "createdAt" 
          FROM messages m2 
          WHERE (m2."senderId" = ${req.user.userId} AND m2."receiverId" = u.id) 
             OR (m2."senderId" = u.id AND m2."receiverId" = ${req.user.userId})
          ORDER BY m2."createdAt" DESC 
          LIMIT 1
        ) as "lastMessageTime",
        (
          SELECT COUNT(*)::int
          FROM messages m2 
          WHERE m2."senderId" = u.id 
            AND m2."receiverId" = ${req.user.userId} 
            AND m2."isRead" = false
        ) as "unreadCount"
      FROM messages m
      JOIN users u ON (
        (m."senderId" = ${req.user.userId} AND m."receiverId" = u.id) OR
        (m."receiverId" = ${req.user.userId} AND m."senderId" = u.id)
      )
      WHERE u.status = 'APPROVED'
      ORDER BY "lastMessageTime" DESC
      LIMIT ${limit} OFFSET ${skip}
    `;

    // Get total count of conversations
    const totalResult = await prisma.$queryRaw<Array<{ count: number }>>`
      SELECT COUNT(DISTINCT 
        CASE 
          WHEN m.senderId = ${req.user.userId} THEN m.receiverId
          ELSE m.senderId
        END
      )::int as count
      FROM messages m
      JOIN users u ON (
        (m."senderId" = ${req.user.userId} AND m."receiverId" = u.id) OR
        (m."receiverId" = ${req.user.userId} AND m."senderId" = u.id)
      )
      WHERE u.status = 'APPROVED'
    `;

    const total = totalResult[0]?.count || 0;

    res.json({
      conversations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get conversation with specific user
 */
export const getConversationWithUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const skip = (page - 1) * limit;

    // Check if the other user exists and is approved
    const otherUser = await prisma.user.findUnique({
      where: {
        id: userId,
        status: UserStatus.APPROVED,
      },
      select: {
        id: true,
        name: true,
        profilePicture: true,
        role: true,
        company: true,
        jobTitle: true,
      },
    });

    if (!otherUser) {
      throw createError("User not found", 404);
    }

    // Check if users are connected (optional - you might want to allow messaging without connection)
    const connection = await prisma.connection.findFirst({
      where: {
        OR: [
          { requesterId: req.user.userId, receiverId: userId, status: "ACCEPTED" },
          { requesterId: userId, receiverId: req.user.userId, status: "ACCEPTED" },
        ],
      },
    });

    if (!connection) {
      throw createError("You can only message users you are connected with", 403);
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where: {
          OR: [
            { senderId: req.user.userId, receiverId: userId },
            { senderId: userId, receiverId: req.user.userId },
          ],
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.message.count({
        where: {
          OR: [
            { senderId: req.user.userId, receiverId: userId },
            { senderId: userId, receiverId: req.user.userId },
          ],
        },
      }),
    ]);

    // Mark messages from the other user as read
    await prisma.message.updateMany({
      where: {
        senderId: userId,
        receiverId: req.user.userId,
        isRead: false,
      },
      data: { isRead: true },
    });

    res.json({
      otherUser,
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Send a message
 */
export const sendMessage = async (req: Request<{}, {}, SendMessageRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { receiverId, content } = req.body;

    if (req.user.userId === receiverId) {
      throw createError("Cannot send message to yourself", 400);
    }

    // Check if receiver exists and is approved
    const receiver = await prisma.user.findUnique({
      where: {
        id: receiverId,
        status: UserStatus.APPROVED,
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!receiver) {
      throw createError("Receiver not found", 404);
    }

    // Check if users are connected
    const connection = await prisma.connection.findFirst({
      where: {
        OR: [
          { requesterId: req.user.userId, receiverId, status: "ACCEPTED" },
          { requesterId: receiverId, receiverId: req.user.userId, status: "ACCEPTED" },
        ],
      },
    });

    if (!connection) {
      throw createError("You can only message users you are connected with", 403);
    }

    // Create message
    const message = await prisma.message.create({
      data: {
        senderId: req.user.userId,
        receiverId,
        content,
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create notification for receiver
    await prisma.notification.create({
      data: {
        userId: receiverId,
        type: "MESSAGE_RECEIVED",
        title: "New Message",
        message: `${req.user.email} sent you a message`,
        data: {
          messageId: message.id,
          senderId: req.user.userId,
          senderName: req.user.email,
        },
      },
    });

    // Send real-time message via WebSocket
    sendRealTimeMessage(receiverId, {
      id: message.id,
      content: message.content,
      sender: message.sender,
      createdAt: message.createdAt,
      isRead: message.isRead,
    });

    res.status(201).json({
      message: "Message sent successfully",
      data: {
        id: message.id,
        content: message.content,
        sender: message.sender,
        receiver: message.receiver,
        createdAt: message.createdAt,
        isRead: message.isRead,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Mark message as read
 */
export const markAsRead = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Find message and verify it belongs to current user as receiver
    const message = await prisma.message.findUnique({
      where: { id },
      select: {
        id: true,
        receiverId: true,
        isRead: true,
      },
    });

    if (!message) {
      throw createError("Message not found", 404);
    }

    if (message.receiverId !== req.user.userId) {
      throw createError("Not authorized to mark this message as read", 403);
    }

    if (message.isRead) {
      return res.json({
        message: "Message already marked as read",
        timestamp: new Date().toISOString(),
      });
    }

    // Mark as read
    await prisma.message.update({
      where: { id },
      data: { isRead: true },
    });

    res.json({
      message: "Message marked as read",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get unread message count
 */
export const getUnreadCount = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const unreadCount = await prisma.message.count({
      where: {
        receiverId: req.user.userId,
        isRead: false,
      },
    });

    res.json({
      unreadCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
