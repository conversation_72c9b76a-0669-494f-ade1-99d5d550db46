"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.get('/dashboard', (req, res) => {
    res.status(501).json({ message: 'Admin dashboard endpoint - Coming soon' });
});
router.get('/users/pending', (req, res) => {
    res.status(501).json({ message: 'Pending users endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=admin.js.map