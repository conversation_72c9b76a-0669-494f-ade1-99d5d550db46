{"name": "college-alumni-portal-backend", "version": "1.0.0", "description": "Backend API for College Alumni Portal - connecting students and alumni for career guidance, networking, and opportunities", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dev:watch": "nodemon --exec ts-node src/index.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["alumni", "portal", "networking", "career", "students", "express", "typescript", "prisma", "postgresql"], "author": "College Alumni Portal Team", "license": "MIT", "type": "commonjs", "dependencies": {"@prisma/client": "^6.12.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "prisma": "^6.12.0", "ws": "^8.18.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.14", "@types/nodemailer": "^6.4.17", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}