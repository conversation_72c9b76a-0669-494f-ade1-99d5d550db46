"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const http_1 = require("http");
const ws_1 = require("ws");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const cors_2 = require("@/config/cors");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler_1 = require("@/middleware/notFoundHandler");
const auth_1 = __importDefault(require("@/routes/auth"));
const users_1 = __importDefault(require("@/routes/users"));
const jobs_1 = __importDefault(require("@/routes/jobs"));
const events_1 = __importDefault(require("@/routes/events"));
const posts_1 = __importDefault(require("@/routes/posts"));
const messages_1 = __importDefault(require("@/routes/messages"));
const notifications_1 = __importDefault(require("@/routes/notifications"));
const admin_1 = __importDefault(require("@/routes/admin"));
const uploads_1 = __importDefault(require("@/routes/uploads"));
const websocket_1 = require("@/services/websocket");
const app = (0, express_1.default)();
const PORT = parseInt(process.env.PORT || "3000");
const WS_PORT = parseInt(process.env.WS_PORT || "3001");
const server = (0, http_1.createServer)(app);
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" },
}));
app.use((0, cors_1.default)(cors_2.corsOptions));
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
app.use((0, cookie_parser_1.default)());
if (process.env.NODE_ENV === "development") {
    app.use((0, morgan_1.default)("dev"));
}
else {
    app.use((0, morgan_1.default)("combined"));
}
app.use(rateLimiter_1.rateLimiter);
app.get("/health", (req, res) => {
    res.status(200).json({
        status: "OK",
        message: "Alumni Portal API is running",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
    });
});
app.use("/api/auth", auth_1.default);
app.use("/api/users", users_1.default);
app.use("/api/jobs", jobs_1.default);
app.use("/api/events", events_1.default);
app.use("/api/posts", posts_1.default);
app.use("/api/messages", messages_1.default);
app.use("/api/notifications", notifications_1.default);
app.use("/api/admin", admin_1.default);
app.use("/api/uploads", uploads_1.default);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
server.listen(PORT, () => {
    console.log(`🚀 Alumni Portal API server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});
const wss = new ws_1.WebSocketServer({ port: WS_PORT });
(0, websocket_1.setupWebSocket)(wss);
console.log(`🔌 WebSocket server running on port ${WS_PORT}`);
process.on("SIGTERM", () => {
    console.log("SIGTERM received, shutting down gracefully");
    server.close(() => {
        console.log("HTTP server closed");
        wss.close(() => {
            console.log("WebSocket server closed");
            process.exit(0);
        });
    });
});
process.on("SIGINT", () => {
    console.log("SIGINT received, shutting down gracefully");
    server.close(() => {
        console.log("HTTP server closed");
        wss.close(() => {
            console.log("WebSocket server closed");
            process.exit(0);
        });
    });
});
exports.default = app;
//# sourceMappingURL=index.js.map