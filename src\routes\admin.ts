import { Router } from "express";
import { authenticate, requireAdmin } from "@/middleware/auth";
import { paginationValidation, idValidation } from "@/middleware/validation";
import * as adminController from "@/controllers/adminController";

const router = Router();

// All routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// Dashboard and metrics
router.get("/dashboard", adminController.getDashboard);
router.get("/metrics", adminController.getMetrics);

// User management
router.get("/users", paginationValidation, adminController.getAllUsers);
router.get("/users/pending", paginationValidation, adminController.getPendingUsers);
router.put("/users/:id/approve", idValidation, adminController.approveUser);
router.put("/users/:id/reject", idValidation, adminController.rejectUser);
router.put("/users/:id/suspend", idValidation, adminController.suspendUser);
router.put("/users/:id/activate", idValidation, adminController.activateUser);
router.delete("/users/:id", idValidation, adminController.deleteUser);

// Content moderation
router.get("/posts/reported", paginationValidation, adminController.getReportedPosts);
router.get("/jobs/all", paginationValidation, adminController.getAllJobs);
router.get("/events/all", paginationValidation, adminController.getAllEvents);
router.put("/posts/:id/hide", idValidation, adminController.hidePost);
router.put("/posts/:id/show", idValidation, adminController.showPost);
router.delete("/posts/:id", idValidation, adminController.deletePost);

// Reports and analytics
router.get("/reports/users", adminController.getUserReports);
router.get("/reports/content", adminController.getContentReports);
router.get("/reports/activity", adminController.getActivityReports);

export default router;
