"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.status(501).json({ message: 'Get jobs endpoint - Coming soon' });
});
router.post('/', (req, res) => {
    res.status(501).json({ message: 'Create job endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=jobs.js.map