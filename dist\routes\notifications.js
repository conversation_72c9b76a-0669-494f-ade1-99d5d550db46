"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.status(501).json({ message: 'Get notifications endpoint - Coming soon' });
});
router.put('/:id/read', (req, res) => {
    res.status(501).json({ message: 'Mark notification as read endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=notifications.js.map