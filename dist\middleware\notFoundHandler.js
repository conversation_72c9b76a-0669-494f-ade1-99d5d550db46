"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = void 0;
const notFoundHandler = (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        message: `The requested endpoint ${req.method} ${req.path} does not exist`,
        timestamp: new Date().toISOString(),
        availableEndpoints: {
            auth: '/api/auth',
            users: '/api/users',
            jobs: '/api/jobs',
            events: '/api/events',
            posts: '/api/posts',
            messages: '/api/messages',
            notifications: '/api/notifications',
            admin: '/api/admin',
            uploads: '/api/uploads',
            health: '/health'
        }
    });
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=notFoundHandler.js.map