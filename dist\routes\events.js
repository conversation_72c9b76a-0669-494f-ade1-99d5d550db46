"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.status(501).json({ message: 'Get events endpoint - Coming soon' });
});
router.post('/', (req, res) => {
    res.status(501).json({ message: 'Create event endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=events.js.map