import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import cookieParser from "cookie-parser";
import { createServer } from "http";
import { WebSocketServer } from "ws";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Import configurations and middleware
import { corsOptions } from "@/config/cors";
import { rateLimiter } from "@/middleware/rateLimiter";
import { errorHandler } from "@/middleware/errorHandler";
import { notFoundHandler } from "@/middleware/notFoundHandler";

// Import routes
import authRoutes from "@/routes/auth";
import userRoutes from "@/routes/users";
import jobRoutes from "@/routes/jobs";
import eventRoutes from "@/routes/events";
import postRoutes from "@/routes/posts";
import messageRoutes from "@/routes/messages";
import notificationRoutes from "@/routes/notifications";
import adminRoutes from "@/routes/admin";
import uploadRoutes from "@/routes/uploads";

// Import WebSocket handler
import { setupWebSocket } from "@/services/websocket";

const app = express();
const PORT = parseInt(process.env.PORT || "3000");
const WS_PORT = parseInt(process.env.WS_PORT || "3001");

// Create HTTP server
const server = createServer(app);

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);

// CORS configuration
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Cookie parser
app.use(cookieParser());

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Rate limiting
app.use(rateLimiter);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Alumni Portal API is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/jobs", jobRoutes);
app.use("/api/events", eventRoutes);
app.use("/api/posts", postRoutes);
app.use("/api/messages", messageRoutes);
app.use("/api/notifications", notificationRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/uploads", uploadRoutes);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start HTTP server
server.listen(PORT, () => {
  console.log(`🚀 Alumni Portal API server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

// Setup WebSocket server
const wss = new WebSocketServer({ port: WS_PORT });
setupWebSocket(wss);

console.log(`🔌 WebSocket server running on port ${WS_PORT}`);

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    wss.close(() => {
      console.log("WebSocket server closed");
      process.exit(0);
    });
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  server.close(() => {
    console.log("HTTP server closed");
    wss.close(() => {
      console.log("WebSocket server closed");
      process.exit(0);
    });
  });
});

export default app;
