"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupWebSocket = void 0;
const setupWebSocket = (wss) => {
    console.log('Setting up WebSocket server...');
    wss.on('connection', (ws, req) => {
        console.log('New WebSocket connection established');
        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message);
                switch (data.type) {
                    case 'auth':
                        ws.userId = data.userId;
                        ws.userRole = data.userRole;
                        ws.send(JSON.stringify({
                            type: 'auth_success',
                            message: 'Authentication successful'
                        }));
                        break;
                    case 'ping':
                        ws.send(JSON.stringify({
                            type: 'pong',
                            timestamp: Date.now()
                        }));
                        break;
                    default:
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Unknown message type'
                        }));
                }
            }
            catch (error) {
                ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Invalid message format'
                }));
            }
        });
        ws.on('close', () => {
            console.log('WebSocket connection closed');
        });
        ws.on('error', (error) => {
            console.error('WebSocket error:', error);
        });
        ws.send(JSON.stringify({
            type: 'welcome',
            message: 'Connected to Alumni Portal WebSocket server'
        }));
    });
    return wss;
};
exports.setupWebSocket = setupWebSocket;
//# sourceMappingURL=websocket.js.map