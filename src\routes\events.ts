import { Router } from "express";
import { authenticate, requireApproved, require<PERSON><PERSON><PERSON> } from "@/middleware/auth";
import { createEventValidation, paginationValidation, idValidation, rsvpValidation } from "@/middleware/validation";
import * as eventController from "@/controllers/eventController";

const router = Router();

// Public routes (no authentication required)
router.get("/", paginationValidation, eventController.getEvents);
router.get("/:id", idValidation, eventController.getEventById);

// Protected routes (authentication required)
router.use(authenticate);
router.use(requireApproved);

// Alumni-only routes
router.post("/", requireAlumni, createEventValidation, eventController.createEvent);
router.put("/:id", requireAlumni, idValidation, createEventValidation, eventController.updateEvent);
router.delete("/:id", requireAlumni, idValidation, eventController.deleteEvent);
router.get("/my/organized", requireAlumni, paginationValidation, eventController.getMyOrganizedEvents);

// RSVP routes (students and alumni can RSVP)
router.post("/:id/rsvp", idValidation, rsvpValidation, eventController.rsvpToEvent);
router.get("/:id/attendees", idValidation, paginationValidation, eventController.getEventAttendees);
router.get("/my/rsvps", paginationValidation, eventController.getMyRSVPs);

export default router;
