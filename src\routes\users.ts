import { Router } from 'express';

const router = Router();

// Placeholder routes - will be implemented in later tasks
router.get('/profile', (req, res) => {
  res.status(501).json({ message: 'Get profile endpoint - Coming soon' });
});

router.put('/profile', (req, res) => {
  res.status(501).json({ message: 'Update profile endpoint - Coming soon' });
});

router.get('/directory', (req, res) => {
  res.status(501).json({ message: 'User directory endpoint - Coming soon' });
});

export default router;
