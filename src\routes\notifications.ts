import { Router } from "express";
import { authenticate, requireApproved } from "@/middleware/auth";
import { paginationValidation, idValidation, notificationPreferencesValidation } from "@/middleware/validation";
import * as notificationController from "@/controllers/notificationController";

const router = Router();

// All routes require authentication
router.use(authenticate);
router.use(requireApproved);

// Notification routes
router.get("/", paginationValidation, notificationController.getNotifications);
router.get("/unread/count", notificationController.getUnreadCount);
router.put("/:id/read", idValidation, notificationController.markAsRead);
router.put("/mark-all-read", notificationController.markAllAsRead);
router.delete("/:id", idValidation, notificationController.deleteNotification);

// Notification preferences
router.get("/preferences", notificationController.getNotificationPreferences);
router.put("/preferences", notificationPreferencesValidation, notificationController.updateNotificationPreferences);

export default router;
