"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.post('/profile-picture', (req, res) => {
    res.status(501).json({ message: 'Upload profile picture endpoint - Coming soon' });
});
router.post('/resume', (req, res) => {
    res.status(501).json({ message: 'Upload resume endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=uploads.js.map