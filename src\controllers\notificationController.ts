import { Request, Response, NextFunction } from 'express';
import { NotificationType } from '@prisma/client';
import { prisma } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { sendRealTimeNotification } from '@/services/websocket';

interface UpdatePreferencesRequest {
  emailJobPosted?: boolean;
  emailEventCreated?: boolean;
  emailMessageReceived?: boolean;
  emailConnectionRequest?: boolean;
  emailPostCreated?: boolean;
  emailSystemUpdates?: boolean;
  inAppJobPosted?: boolean;
  inAppEventCreated?: boolean;
  inAppMessageReceived?: boolean;
  inAppConnectionRequest?: boolean;
  inAppPostCreated?: boolean;
  inAppSystemUpdates?: boolean;
  emailDigest?: boolean;
  emailDigestFrequency?: string;
}

/**
 * Get notifications for current user
 */
export const getNotifications = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const type = req.query.type as NotificationType;
    const unreadOnly = req.query.unreadOnly === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      userId: req.user.userId,
    };

    if (type) {
      where.type = type;
    }

    if (unreadOnly) {
      where.isRead = false;
    }

    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.notification.count({ where })
    ]);

    res.json({
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get unread notification count
 */
export const getUnreadCount = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const unreadCount = await prisma.notification.count({
      where: {
        userId: req.user.userId,
        isRead: false
      }
    });

    res.json({
      unreadCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Mark notification as read
 */
export const markAsRead = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;

    // Find notification and verify it belongs to current user
    const notification = await prisma.notification.findUnique({
      where: { id },
      select: {
        id: true,
        userId: true,
        isRead: true,
      }
    });

    if (!notification) {
      throw createError('Notification not found', 404);
    }

    if (notification.userId !== req.user.userId) {
      throw createError('Not authorized to mark this notification as read', 403);
    }

    if (notification.isRead) {
      return res.json({
        message: 'Notification already marked as read',
        timestamp: new Date().toISOString(),
      });
    }

    // Mark as read
    await prisma.notification.update({
      where: { id },
      data: { isRead: true }
    });

    res.json({
      message: 'Notification marked as read',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Mark all notifications as read
 */
export const markAllAsRead = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const result = await prisma.notification.updateMany({
      where: {
        userId: req.user.userId,
        isRead: false
      },
      data: { isRead: true }
    });

    res.json({
      message: `${result.count} notifications marked as read`,
      count: result.count,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete notification
 */
export const deleteNotification = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const { id } = req.params;

    // Find notification and verify it belongs to current user
    const notification = await prisma.notification.findUnique({
      where: { id },
      select: {
        id: true,
        userId: true,
      }
    });

    if (!notification) {
      throw createError('Notification not found', 404);
    }

    if (notification.userId !== req.user.userId) {
      throw createError('Not authorized to delete this notification', 403);
    }

    // Delete notification
    await prisma.notification.delete({
      where: { id }
    });

    res.json({
      message: 'Notification deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get notification preferences
 */
export const getNotificationPreferences = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    let preferences = await prisma.notificationPreferences.findUnique({
      where: { userId: req.user.userId }
    });

    // Create default preferences if they don't exist
    if (!preferences) {
      preferences = await prisma.notificationPreferences.create({
        data: {
          userId: req.user.userId
        }
      });
    }

    res.json({
      preferences,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update notification preferences
 */
export const updateNotificationPreferences = async (req: Request<{}, {}, UpdatePreferencesRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError('User not authenticated', 401);
    }

    const updateData = req.body;

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Upsert preferences
    const preferences = await prisma.notificationPreferences.upsert({
      where: { userId: req.user.userId },
      update: cleanedData,
      create: {
        userId: req.user.userId,
        ...cleanedData
      }
    });

    res.json({
      message: 'Notification preferences updated successfully',
      preferences,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
