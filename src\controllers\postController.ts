import { Request, Response, NextFunction } from "express";
import { PostType } from "@prisma/client";
import { prisma } from "@/config/database";
import { createError } from "@/middleware/errorHandler";

interface CreatePostRequest {
  title: string;
  content: string;
  type: PostType;
  isPublic: boolean;
  imageUrl?: string;
}

/**
 * Get all public posts with search and filtering
 */
export const getPosts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const type = req.query.type as PostType;
    const authorId = req.query.authorId as string;

    const skip = (page - 1) * limit;

    // Build where clause - only show public posts for non-authenticated users
    const where: any = {
      isPublic: true,
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (authorId) {
      where.authorId = authorId;
    }

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
              role: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.post.count({ where }),
    ]);

    res.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get post by ID
 */
export const getPostById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            role: true,
            linkedinUrl: true,
            showLinkedin: true,
          },
        },
      },
    });

    if (!post) {
      throw createError("Post not found", 404);
    }

    // Check if post is public or if user is authenticated and is the author
    if (!post.isPublic) {
      if (!req.user || req.user.userId !== post.authorId) {
        throw createError("Post not found", 404);
      }
    }

    // Filter author's contact info based on privacy settings
    const filteredPost = {
      ...post,
      author: {
        ...post.author,
        linkedinUrl: post.author.showLinkedin ? post.author.linkedinUrl : null,
      },
    };

    res.json({
      post: filteredPost,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new post (Alumni only)
 */
export const createPost = async (req: Request<{}, {}, CreatePostRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const postData = req.body;

    const post = await prisma.post.create({
      data: {
        ...postData,
        authorId: req.user.userId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            role: true,
          },
        },
      },
    });

    // Create notifications for followers/connections if post is public
    if (post.isPublic) {
      // Get all users who are connected to this author
      const connections = await prisma.connection.findMany({
        where: {
          OR: [
            { requesterId: req.user.userId, status: "ACCEPTED" },
            { receiverId: req.user.userId, status: "ACCEPTED" },
          ],
        },
        select: {
          requesterId: true,
          receiverId: true,
        },
      });

      // Extract unique user IDs (excluding the author)
      const followerIds = new Set<string>();
      connections.forEach((conn) => {
        if (conn.requesterId !== req.user!.userId) {
          followerIds.add(conn.requesterId);
        }
        if (conn.receiverId !== req.user!.userId) {
          followerIds.add(conn.receiverId);
        }
      });

      // Create notifications for followers
      if (followerIds.size > 0) {
        await prisma.notification.createMany({
          data: Array.from(followerIds).map((userId) => ({
            userId,
            type: "POST_CREATED" as const,
            title: "New Post",
            message: `${req.user!.email} shared a new ${post.type.toLowerCase()}: ${post.title}`,
            data: {
              postId: post.id,
              authorId: req.user!.userId,
              postType: post.type,
            },
          })),
        });
      }
    }

    res.status(201).json({
      message: "Post created successfully",
      post,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update post (Alumni only - own posts)
 */
export const updatePost = async (
  req: Request<{ id: string }, {}, CreatePostRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Check if post exists and belongs to current user
    const existingPost = await prisma.post.findUnique({
      where: { id },
      select: {
        id: true,
        authorId: true,
      },
    });

    if (!existingPost) {
      throw createError("Post not found", 404);
    }

    if (existingPost.authorId !== req.user.userId) {
      throw createError("Not authorized to update this post", 403);
    }

    const updatedPost = await prisma.post.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            role: true,
          },
        },
      },
    });

    res.json({
      message: "Post updated successfully",
      post: updatedPost,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete post (Alumni only - own posts)
 */
export const deletePost = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Check if post exists and belongs to current user
    const existingPost = await prisma.post.findUnique({
      where: { id },
      select: {
        id: true,
        authorId: true,
        title: true,
      },
    });

    if (!existingPost) {
      throw createError("Post not found", 404);
    }

    if (existingPost.authorId !== req.user.userId) {
      throw createError("Not authorized to delete this post", 403);
    }

    // Delete the post
    await prisma.post.delete({
      where: { id },
    });

    res.json({
      message: "Post deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get posts created by current user (Alumni only)
 */
export const getMyPosts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where: {
          authorId: req.user.userId,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
              role: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.post.count({
        where: {
          authorId: req.user.userId,
        },
      }),
    ]);

    res.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get dashboard feed for authenticated user
 */
export const getDashboardFeed = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Get user's connections
    const connections = await prisma.connection.findMany({
      where: {
        OR: [
          { requesterId: req.user.userId, status: "ACCEPTED" },
          { receiverId: req.user.userId, status: "ACCEPTED" },
        ],
      },
      select: {
        requesterId: true,
        receiverId: true,
      },
    });

    // Extract unique user IDs (excluding current user)
    const connectedUserIds = new Set<string>();
    connections.forEach((conn) => {
      if (conn.requesterId !== req.user!.userId) {
        connectedUserIds.add(conn.requesterId);
      }
      if (conn.receiverId !== req.user!.userId) {
        connectedUserIds.add(conn.receiverId);
      }
    });

    // Build where clause to show posts from connected users + public posts
    const where: any = {
      OR: [
        { isPublic: true }, // All public posts
        {
          authorId: { in: Array.from(connectedUserIds) }, // Posts from connections
          isPublic: false,
        },
      ],
    };

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
              role: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.post.count({ where }),
    ]);

    res.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
