import { Request, Response, NextFunction } from "express";
import { prisma } from "@/config/database";
import { createError } from "@/middleware/errorHandler";

interface CreateEventRequest {
  title: string;
  description: string;
  location?: string;
  imageUrl?: string;
  startTime: string;
  endTime?: string;
  isOnline: boolean;
  meetingUrl?: string;
  maxAttendees?: number;
}

interface RSVPRequest {
  status: "GOING" | "MAYBE" | "NOT_GOING";
}

/**
 * Get all events with search and filtering
 */
export const getEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const upcoming = req.query.upcoming === "true";
    const isOnline = req.query.isOnline === "true";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      isActive: true,
    };

    if (upcoming) {
      where.startTime = { gte: new Date() };
    }

    if (isOnline !== undefined) {
      where.isOnline = isOnline;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              company: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              rsvps: {
                where: { status: "GOING" },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { startTime: "asc" },
      }),
      prisma.event.count({ where }),
    ]);

    res.json({
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get event by ID
 */
export const getEventById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const event = await prisma.event.findUnique({
      where: {
        id,
        isActive: true,
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
            linkedinUrl: true,
            showLinkedin: true,
          },
        },
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    // Check if current user has RSVP'd (if authenticated)
    let userRSVP = null;
    if (req.user) {
      const rsvp = await prisma.eventRSVP.findUnique({
        where: {
          eventId_userId: {
            eventId: id,
            userId: req.user.userId,
          },
        },
      });
      userRSVP = rsvp?.status || null;
    }

    // Filter organizer's contact info based on privacy settings
    const filteredEvent = {
      ...event,
      organizer: {
        ...event.organizer,
        linkedinUrl: event.organizer.showLinkedin ? event.organizer.linkedinUrl : null,
      },
      userRSVP,
    };

    res.json({
      event: filteredEvent,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new event (Alumni only)
 */
export const createEvent = async (req: Request<{}, {}, CreateEventRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const eventData = req.body;

    // Parse dates
    const startTime = new Date(eventData.startTime);
    const endTime = eventData.endTime ? new Date(eventData.endTime) : null;

    // Validate dates
    if (startTime < new Date()) {
      throw createError("Event start time cannot be in the past", 400);
    }

    if (endTime && endTime <= startTime) {
      throw createError("Event end time must be after start time", 400);
    }

    // Validate online event requirements
    if (eventData.isOnline && !eventData.meetingUrl) {
      throw createError("Meeting URL is required for online events", 400);
    }

    const event = await prisma.event.create({
      data: {
        ...eventData,
        startTime,
        endTime,
        organizerId: req.user.userId,
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
      },
    });

    // Create notifications for followers/connections
    // TODO: Implement notification system for event creation

    res.status(201).json({
      message: "Event created successfully",
      event,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update event (Alumni only - own events)
 */
export const updateEvent = async (
  req: Request<{ id: string }, {}, CreateEventRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const updateData = req.body;

    // Check if event exists and belongs to current user
    const existingEvent = await prisma.event.findUnique({
      where: { id },
      select: {
        id: true,
        organizerId: true,
        startTime: true,
      },
    });

    if (!existingEvent) {
      throw createError("Event not found", 404);
    }

    if (existingEvent.organizerId !== req.user.userId) {
      throw createError("Not authorized to update this event", 403);
    }

    // Check if event has already started
    if (existingEvent.startTime < new Date()) {
      throw createError("Cannot update an event that has already started", 400);
    }

    // Parse dates
    const startTime = new Date(updateData.startTime);
    const endTime = updateData.endTime ? new Date(updateData.endTime) : null;

    // Validate dates
    if (startTime < new Date()) {
      throw createError("Event start time cannot be in the past", 400);
    }

    if (endTime && endTime <= startTime) {
      throw createError("Event end time must be after start time", 400);
    }

    // Validate online event requirements
    if (updateData.isOnline && !updateData.meetingUrl) {
      throw createError("Meeting URL is required for online events", 400);
    }

    const updatedEvent = await prisma.event.update({
      where: { id },
      data: {
        ...updateData,
        startTime,
        endTime,
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            company: true,
            jobTitle: true,
          },
        },
      },
    });

    res.json({
      message: "Event updated successfully",
      event: updatedEvent,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete event (Alumni only - own events)
 */
export const deleteEvent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;

    // Check if event exists and belongs to current user
    const existingEvent = await prisma.event.findUnique({
      where: { id },
      select: {
        id: true,
        organizerId: true,
        title: true,
        startTime: true,
      },
    });

    if (!existingEvent) {
      throw createError("Event not found", 404);
    }

    if (existingEvent.organizerId !== req.user.userId) {
      throw createError("Not authorized to delete this event", 403);
    }

    // Check if event has already started
    if (existingEvent.startTime < new Date()) {
      throw createError("Cannot delete an event that has already started", 400);
    }

    // Soft delete by setting isActive to false
    await prisma.event.update({
      where: { id },
      data: { isActive: false },
    });

    // Notify all attendees about event cancellation
    const attendees = await prisma.eventRSVP.findMany({
      where: {
        eventId: id,
        status: "GOING",
      },
      select: { userId: true },
    });

    // Create notifications for attendees
    if (attendees.length > 0) {
      await prisma.notification.createMany({
        data: attendees.map((attendee) => ({
          userId: attendee.userId,
          type: "EVENT_CREATED" as const,
          title: "Event Cancelled",
          message: `The event "${existingEvent.title}" has been cancelled by the organizer`,
          data: {
            eventId: id,
            eventTitle: existingEvent.title,
          },
        })),
      });
    }

    res.json({
      message: "Event deleted successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get events organized by current user (Alumni only)
 */
export const getMyOrganizedEvents = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where: {
          organizerId: req.user.userId,
          isActive: true,
        },
        include: {
          _count: {
            select: {
              rsvps: {
                where: { status: "GOING" },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { startTime: "asc" },
      }),
      prisma.event.count({
        where: {
          organizerId: req.user.userId,
          isActive: true,
        },
      }),
    ]);

    res.json({
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * RSVP to an event
 */
export const rsvpToEvent = async (req: Request<{ id: string }, {}, RSVPRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id: eventId } = req.params;
    const { status } = req.body;

    // Check if event exists and is active
    const event = await prisma.event.findUnique({
      where: {
        id: eventId,
        isActive: true,
      },
      select: {
        id: true,
        title: true,
        startTime: true,
        maxAttendees: true,
        organizerId: true,
        organizer: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            rsvps: {
              where: { status: "GOING" },
            },
          },
        },
      },
    });

    if (!event) {
      throw createError("Event not found or no longer active", 404);
    }

    // Check if event has already started
    if (event.startTime < new Date()) {
      throw createError("Cannot RSVP to an event that has already started", 400);
    }

    // Check if user is trying to RSVP to their own event
    if (event.organizerId === req.user.userId) {
      throw createError("Cannot RSVP to your own event", 400);
    }

    // Check if event is full (only for GOING status)
    if (status === "GOING" && event.maxAttendees && event._count.rsvps >= event.maxAttendees) {
      throw createError("Event is full", 409);
    }

    // Upsert RSVP
    const rsvp = await prisma.eventRSVP.upsert({
      where: {
        eventId_userId: {
          eventId,
          userId: req.user.userId,
        },
      },
      update: { status },
      create: {
        eventId,
        userId: req.user.userId,
        status,
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            startTime: true,
          },
        },
      },
    });

    // Create notification for event organizer (only for GOING status)
    if (status === "GOING") {
      await prisma.notification.create({
        data: {
          userId: event.organizerId,
          type: "EVENT_CREATED",
          title: "New Event RSVP",
          message: `${req.user.email} will attend your event: ${event.title}`,
          data: {
            eventId,
            rsvpId: rsvp.id,
            attendeeId: req.user.userId,
          },
        },
      });
    }

    res.json({
      message: `RSVP updated to ${status}`,
      rsvp: {
        id: rsvp.id,
        status: rsvp.status,
        event: rsvp.event,
        updatedAt: rsvp.updatedAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get event attendees
 */
export const getEventAttendees = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id: eventId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Check if event exists
    const event = await prisma.event.findUnique({
      where: {
        id: eventId,
        isActive: true,
      },
      select: {
        id: true,
        title: true,
        organizerId: true,
      },
    });

    if (!event) {
      throw createError("Event not found", 404);
    }

    const [attendees, total] = await Promise.all([
      prisma.eventRSVP.findMany({
        where: {
          eventId,
          status: "GOING",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              role: true,
              course: true,
              batch: true,
              company: true,
              jobTitle: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.eventRSVP.count({
        where: {
          eventId,
          status: "GOING",
        },
      }),
    ]);

    res.json({
      event: {
        id: event.id,
        title: event.title,
      },
      attendees: attendees.map((rsvp) => ({
        id: rsvp.id,
        user: rsvp.user,
        rsvpDate: rsvp.createdAt,
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current user's RSVPs
 */
export const getMyRSVPs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [rsvps, total] = await Promise.all([
      prisma.eventRSVP.findMany({
        where: { userId: req.user.userId },
        include: {
          event: {
            select: {
              id: true,
              title: true,
              description: true,
              location: true,
              startTime: true,
              endTime: true,
              isOnline: true,
              isActive: true,
              organizer: {
                select: {
                  id: true,
                  name: true,
                  profilePicture: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { event: { startTime: "asc" } },
      }),
      prisma.eventRSVP.count({ where: { userId: req.user.userId } }),
    ]);

    res.json({
      rsvps,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
