import { Router } from "express";
import { authenticate, requireApproved } from "@/middleware/auth";
import { paginationValidation, idValidation, sendMessageValidation } from "@/middleware/validation";
import * as messageController from "@/controllers/messageController";

const router = Router();

// All routes require authentication
router.use(authenticate);
router.use(requireApproved);

// Message routes
router.get("/conversations", paginationValidation, messageController.getConversations);
router.get("/conversations/:userId", idValidation, paginationValidation, messageController.getConversationWithUser);
router.post("/send", sendMessageValidation, messageController.sendMessage);
router.put("/:id/read", idValidation, messageController.markAsRead);
router.get("/unread/count", messageController.getUnreadCount);

export default router;
