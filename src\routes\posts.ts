import { Router } from "express";
import { authenticate, requireApproved, require<PERSON><PERSON><PERSON> } from "@/middleware/auth";
import { paginationValidation, idValidation, createPostValidation } from "@/middleware/validation";
import * as postController from "@/controllers/postController";

const router = Router();

// Public routes (no authentication required)
router.get("/", paginationValidation, postController.getPosts);
router.get("/:id", idValidation, postController.getPostById);

// Protected routes (authentication required)
router.use(authenticate);
router.use(requireApproved);

// Alumni-only routes
router.post("/", require<PERSON><PERSON><PERSON>, createPostValidation, postController.createPost);
router.put("/:id", requireAlumni, idValidation, createPostValidation, postController.updatePost);
router.delete("/:id", requireAlumni, idValidation, postController.deletePost);
router.get("/my/posts", require<PERSON><PERSON><PERSON>, paginationValidation, postController.getMyPosts);

// Feed routes (for dashboard)
router.get("/feed/dashboard", paginationValidation, postController.getDashboardFeed);

export default router;
