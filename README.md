# College Alumni Portal - Backend API

A comprehensive backend API for connecting college students and alumni for career guidance, networking opportunities, job postings, and community engagement.

## 🚀 Features

- **User Management**: Registration, authentication, and profile management for students and alumni
- **Job Portal**: Job and internship posting, searching, and application management
- **Event Management**: Event creation, RSVP functionality, and community engagement
- **Messaging System**: Real-time messaging between students and alumni
- **Notification System**: In-app and email notifications
- **Admin Panel**: User approval, content moderation, and administrative tools
- **File Upload**: Profile pictures, resumes, and event images via Cloudinary

## 🛠️ Tech Stack

- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt
- **Real-time**: WebSocket
- **File Upload**: Cloudinary
- **Email**: Nodemailer
- **Validation**: express-validator
- **Security**: Helmet, CORS, Rate limiting

## 📋 Prerequisites

- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- npm or yarn

## 🔧 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```
Edit the `.env` file with your configuration values.

4. Set up the database:
```bash
npm run prisma:migrate
npm run prisma:generate
```

5. Seed the database (optional):
```bash
npm run prisma:seed
```

## 🚀 Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm run build
npm start
```

## 📚 API Documentation

The API will be documented using OpenAPI/Swagger (coming soon).

### Base URL
- Development: `http://localhost:3000`
- WebSocket: `ws://localhost:3001`

### Health Check
```
GET /health
```

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 📁 Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Route controllers
├── middleware/      # Custom middleware
├── models/          # Database models (Prisma)
├── routes/          # API routes
├── services/        # Business logic services
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── index.ts         # Application entry point
```

## 🧪 Testing

```bash
npm test
```

## 📝 Environment Variables

See `.env.example` for all required environment variables.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
