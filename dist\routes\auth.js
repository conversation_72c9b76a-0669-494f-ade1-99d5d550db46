"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.post('/register', (req, res) => {
    res.status(501).json({ message: 'Registration endpoint - Coming soon' });
});
router.post('/login', (req, res) => {
    res.status(501).json({ message: 'Login endpoint - Coming soon' });
});
router.post('/logout', (req, res) => {
    res.status(501).json({ message: 'Logout endpoint - Coming soon' });
});
router.post('/refresh', (req, res) => {
    res.status(501).json({ message: 'Token refresh endpoint - Coming soon' });
});
exports.default = router;
//# sourceMappingURL=auth.js.map